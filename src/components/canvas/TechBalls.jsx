import { Suspense, useMemo } from "react";
import { Canvas } from "@react-three/fiber";
import {
  Decal,
  Float,
  OrbitControls,
  useTexture,
} from "@react-three/drei";

import CanvasLoader from "../Loader";

const Ball = ({ position, imgUrl, index }) => {
  const [decal] = useTexture([imgUrl]);

  return (
    <Float 
      speed={1.75} 
      rotationIntensity={1} 
      floatIntensity={2}
      position={position}
    >
      <mesh castShadow={false} receiveShadow={false} scale={1.5}>
        <icosahedronGeometry args={[1, 1]} />
        <meshStandardMaterial
          color='#fff8eb'
          polygonOffset
          polygonOffsetFactor={-5}
          flatShading
        />
        <Decal
          position={[0, 0, 1]}
          rotation={[2 * Math.PI, 0, 6.25]}
          scale={1}
          map={decal}
          flatShading
        />
      </mesh>
    </Float>
  );
};

const TechBallsCanvas = ({ technologies }) => {
  // Calculate positions for balls in a grid
  const ballPositions = useMemo(() => {
    const positions = [];
    const cols = Math.ceil(Math.sqrt(technologies.length));
    const spacing = 4;
    
    technologies.forEach((_, index) => {
      const row = Math.floor(index / cols);
      const col = index % cols;
      const x = (col - (cols - 1) / 2) * spacing;
      const y = (row - Math.floor(technologies.length / cols) / 2) * spacing;
      const z = 0;
      positions.push([x, y, z]);
    });
    
    return positions;
  }, [technologies.length]);

  return (
    <Canvas
      frameloop='demand'
      dpr={[1, 1.5]}
      gl={{ 
        preserveDrawingBuffer: true,
        antialias: false,
        alpha: true,
        powerPreference: "high-performance"
      }}
      camera={{ position: [0, 0, 20], fov: 45 }}
    >
      <Suspense fallback={<CanvasLoader />}>
        <ambientLight intensity={0.25} />
        <directionalLight position={[0, 0, 0.05]} />
        
        <OrbitControls 
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          autoRotate={false}
          minDistance={10}
          maxDistance={50}
        />
        
        {technologies.map((technology, index) => (
          <Ball
            key={technology.name}
            position={ballPositions[index]}
            imgUrl={technology.icon}
            index={index}
          />
        ))}
      </Suspense>
    </Canvas>
  );
};

export default TechBallsCanvas;
