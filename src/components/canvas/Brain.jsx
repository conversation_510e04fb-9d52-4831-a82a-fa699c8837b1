import React, { Suspense, useRef, useMemo } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { OrbitControls, Preload, useGLTF } from "@react-three/drei";
import * as THREE from "three";

import CanvasLoader from "../Loader";

const Particles = () => {
  const meshRef = useRef();
  const particleCount = 8000; // Optimized particle count for balanced performance and visual quality

  // Create particle positions and properties with chaotic movement data
  const particles = useMemo(() => {
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const velocities = new Float32Array(particleCount * 3);
    const phases = new Float32Array(particleCount);
    const frequencies = new Float32Array(particleCount);
    const amplitudes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      // Create particles very close to brain surface - even distribution
      const radius = 1.35 + Math.random() * 0.5; // Slightly expanded: 1.35-1.85 for better coverage
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      // Generate base spherical coordinates
      const x = radius * Math.sin(phi) * Math.cos(theta);
      const y = radius * Math.sin(phi) * Math.sin(theta);
      const z = radius * Math.cos(phi);

      // Apply brain-shaped distribution for even coverage on all sides
      positions[i * 3] = x * 1.15; // Slightly more width to ensure full side coverage
      positions[i * 3 + 1] = y * 0.85 + 0.05; // Compressed height with minimal offset
      positions[i * 3 + 2] = z * 1.05; // Slight depth extension for complete coverage

      // White/light grey particles to match the reference image
      const brightness = 0.6 + Math.random() * 0.4; // Random brightness 0.6-1.0
      colors[i * 3] = brightness; // R
      colors[i * 3 + 1] = brightness; // G
      colors[i * 3 + 2] = brightness; // B

      sizes[i] = Math.random() * 1.5 + 0.5; // Slightly smaller particles for better density

      // Chaotic movement properties for neural-like activity
      velocities[i * 3] = (Math.random() - 0.5) * 0.02; // Random X velocity
      velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.02; // Random Y velocity
      velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.02; // Random Z velocity

      phases[i] = Math.random() * Math.PI * 2; // Random phase for each particle
      frequencies[i] = 0.5 + Math.random() * 2; // Random frequency (0.5-2.5)
      amplitudes[i] = 0.001 + Math.random() * 0.004; // Random amplitude (0.001-0.005)
    }

    return { positions, colors, sizes, velocities, phases, frequencies, amplitudes };
  }, []);

  // Chaotic neural-like particle animation
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.1;

      const positions = meshRef.current.geometry.attributes.position.array;
      const time = state.clock.elapsedTime;

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // Get particle's unique properties
        const phase = particles.phases[i];
        const frequency = particles.frequencies[i];
        const amplitude = particles.amplitudes[i];

        // Create chaotic movement with multiple noise sources
        const noiseX = Math.sin(time * frequency + phase) * amplitude;
        const noiseY = Math.cos(time * frequency * 1.3 + phase + 1.5) * amplitude;
        const noiseZ = Math.sin(time * frequency * 0.8 + phase + 3) * amplitude;

        // Add random jitter for neural firing effect
        const jitterX = (Math.random() - 0.5) * 0.0008;
        const jitterY = (Math.random() - 0.5) * 0.0008;
        const jitterZ = (Math.random() - 0.5) * 0.0008;

        // Add turbulent motion with varying intensities
        const turbulence = Math.sin(time * 3 + i * 0.1) * 0.002;
        const turbulentX = Math.sin(time * 2.1 + i * 0.05) * turbulence;
        const turbulentY = Math.cos(time * 1.7 + i * 0.08) * turbulence;
        const turbulentZ = Math.sin(time * 2.3 + i * 0.03) * turbulence;

        // Random directional changes (neural firing simulation)
        if (Math.random() < 0.002) { // 0.2% chance per frame for sudden movement
          particles.velocities[i3] = (Math.random() - 0.5) * 0.03;
          particles.velocities[i3 + 1] = (Math.random() - 0.5) * 0.03;
          particles.velocities[i3 + 2] = (Math.random() - 0.5) * 0.03;
        }

        // Apply velocity decay for more natural movement
        particles.velocities[i3] *= 0.98;
        particles.velocities[i3 + 1] *= 0.98;
        particles.velocities[i3 + 2] *= 0.98;

        // Combine all movement types for chaotic neural activity
        positions[i3] += noiseX + jitterX + turbulentX + particles.velocities[i3];
        positions[i3 + 1] += noiseY + jitterY + turbulentY + particles.velocities[i3 + 1];
        positions[i3 + 2] += noiseZ + jitterZ + turbulentZ + particles.velocities[i3 + 2];
      }

      meshRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <points ref={meshRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={particles.positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={particles.colors}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-size"
          count={particleCount}
          array={particles.sizes}
          itemSize={1}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.02}
        vertexColors
        transparent
        opacity={0.6}
        sizeAttenuation
        blending={THREE.AdditiveBlending}
      />
    </points>
  );
};

const Brain = () => {
  const brain = useGLTF("./brain/particle_ai_brain.gltf");

  // Apply greyish material to the brain
  React.useEffect(() => {
    if (brain.scene) {
      brain.scene.traverse((child) => {
        if (child.isMesh) {
          // Clean, smooth brain material with natural lighting-based gradation
          child.material = new THREE.MeshStandardMaterial({
            color: new THREE.Color(0.02, 0.02, 0.02), // Pure black/very dark base
            metalness: 0.05, // Minimal metalness for organic appearance
            roughness: 0.7, // Smooth but not reflective
            emissive: new THREE.Color(0, 0, 0), // No artificial glow
            // Let natural lighting create depth and shadows
          });

          // No wireframe or overlay meshes - clean smooth surface only
        }
      });
    }
  }, [brain.scene]);

  return (
    <group>
      <primitive object={brain.scene} scale={1.2} position-y={0} rotation-y={0} />
      <Particles />
    </group>
  );
};

const BrainCanvas = () => {
  return (
    <Canvas
      shadows
      frameloop='demand'
      dpr={[1, 2]}
      gl={{ preserveDrawingBuffer: true }}
      camera={{
        fov: 45,
        near: 0.1,
        far: 200,
        position: [-4, 3, 6],
      }}
    >
      <Suspense fallback={<CanvasLoader />}>
        <OrbitControls
          autoRotate
          enableZoom={false}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 2}
        />
        <Brain />

        <Preload all />
      </Suspense>
    </Canvas>
  );
};

export default BrainCanvas;
