import React from 'react';

class WebGLErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log WebGL errors
    console.error('WebGL Error caught by boundary:', error, errorInfo);
    
    // Check if it's a WebGL-related error
    if (error.message && (
      error.message.includes('WebGL') || 
      error.message.includes('context') ||
      error.message.includes('CONTEXT_LOST')
    )) {
      console.warn('WebGL context error detected, rendering fallback');
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return this.props.fallback || (
        <div className="flex items-center justify-center p-8 text-center">
          <div>
            <p className="text-gray-400 mb-4">3D graphics unavailable</p>
            <p className="text-sm text-gray-500">
              Your browser or device doesn't support WebGL or ran out of graphics memory.
            </p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default WebGLErrorBoundary;
