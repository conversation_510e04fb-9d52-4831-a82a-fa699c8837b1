import React, { useState, useEffect } from "react";

import { BallCanvas } from "./canvas";
import TechBallsCanvas from "./canvas/TechBalls";
import WebGLErrorBoundary from "./WebGLErrorBoundary";
import { SectionWrapper } from "../hoc";
import { technologies } from "../constants";

// Fallback component for when WebGL fails
const TechFallback = () => {
  return (
    <div className='flex flex-row flex-wrap justify-center gap-10'>
      {technologies.map((technology) => (
        <div className='w-28 h-28 flex items-center justify-center bg-tertiary rounded-full' key={technology.name}>
          <img
            src={technology.icon}
            alt={technology.name}
            className="w-16 h-16 object-contain"
          />
        </div>
      ))}
    </div>
  );
};

const Tech = () => {
  const [useOptimized, setUseOptimized] = useState(false);
  const [webglError, setWebglError] = useState(false);

  useEffect(() => {
    // Listen for WebGL context lost errors
    const handleWebGLError = () => {
      console.warn("WebGL context lost, switching to fallback");
      setWebglError(true);
    };

    window.addEventListener('webglcontextlost', handleWebGLError);

    // Check if we should use optimized version (single canvas)
    // Temporarily disable 3D to fix WebGL context issues
    const shouldUseOptimized = false; // Disable 3D temporarily
    setUseOptimized(shouldUseOptimized);

    // Force fallback mode to avoid WebGL issues for now
    setWebglError(true);

    return () => {
      window.removeEventListener('webglcontextlost', handleWebGLError);
    };
  }, []);

  // If WebGL error occurred, use fallback
  if (webglError) {
    return <TechFallback />;
  }

  // Use single canvas approach for better performance
  if (useOptimized) {
    return (
      <WebGLErrorBoundary fallback={<TechFallback />}>
        <div className="w-full h-96">
          <TechBallsCanvas technologies={technologies} />
        </div>
      </WebGLErrorBoundary>
    );
  }

  // Original approach with individual canvases (limited to fewer items)
  return (
    <WebGLErrorBoundary fallback={<TechFallback />}>
      <div className='flex flex-row flex-wrap justify-center gap-10'>
        {technologies.slice(0, 8).map((technology) => (
          <div className='w-28 h-28' key={technology.name}>
            <BallCanvas icon={technology.icon} />
          </div>
        ))}
      </div>
    </WebGLErrorBoundary>
  );
};

export default SectionWrapper(Tech, "");
